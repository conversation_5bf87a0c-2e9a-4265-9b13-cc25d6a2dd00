import { createClient } from "@supabase/supabase-js";
import { Hono } from "hono";
import {
  handleHealthCheck,
  handleQuickHealthCheck,
  handleRLSValidation,
  testDatabaseConnection,
  validateDatabaseEnvironment,
} from "../lib/database";
import { createErrorResponse, createSuccessResponse } from "../types/api";

// Health check result interface
interface HealthCheckResult {
  service: string;
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  response_time_ms: number;
  details: Record<string, any>;
  version: string;
}

const health = new Hono();

class HealthCheckService {
  private static startTime = Date.now();

  static async checkDatabaseHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      const supabase = createClient(
        process.env.SUPABASE_URL || "",
        process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      );

      // Test basic database connectivity
      const { data: _data, error } = await supabase
        .from("patients")
        .select("count")
        .limit(1);

      const responseTime = Date.now() - startTime;

      if (error) {
        return {
          service: "database",
          status: "unhealthy",
          timestamp: new Date().toISOString(),
          response_time_ms: responseTime,
          details: { error: error instanceof Error ? error.message : String(error) },
          version: "1.0.0",
        };
      }

      return {
        service: "database",
        status: responseTime < 1000 ? "healthy" : "degraded",
        timestamp: new Date().toISOString(),
        response_time_ms: responseTime,
        details: {
          connection: "ok",
          query_test: "passed",
        },
        version: "1.0.0",
      };
    } catch (error) {
      return {
        service: "database",
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - startTime,
        details: { error: error instanceof Error ? error.message : String(error) },
        version: "1.0.0",
      };
    }
  }

  static async checkSupabaseHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      const supabase = createClient(
        process.env.SUPABASE_URL || "",
        process.env.SUPABASE_ANON_KEY || "",
      );

      // Test Supabase API connectivity
      const { data: _data, error } = await supabase.auth.getSession();
      const responseTime = Date.now() - startTime;

      return {
        service: "supabase",
        status: responseTime < 1000 && !error ? "healthy" : "degraded",
        timestamp: new Date().toISOString(),
        response_time_ms: responseTime,
        details: {
          auth_api: error ? "error" : "ok",
          error: error?.message,
        },
        version: "1.0.0",
      };
    } catch (error) {
      return {
        service: "supabase",
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - startTime,
        details: { error: error instanceof Error ? error.message : String(error) },
        version: "1.0.0",
      };
    }
  }

  static async checkRedisHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      // In a real implementation, you would check Redis connectivity here
      // For now, we'll simulate a Redis health check
      const responseTime = Date.now() - startTime;

      return {
        service: "redis",
        status: "healthy",
        timestamp: new Date().toISOString(),
        response_time_ms: responseTime,
        details: {
          connection: "ok",
          cache_test: "passed",
        },
        version: "1.0.0",
      };
    } catch (error) {
      return {
        service: "redis",
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        response_time_ms: Date.now() - startTime,
        details: { error: error instanceof Error ? error.message : String(error) },
        version: "1.0.0",
      };
    }
  }

  static getSystemInfo(): Record<string, any> {
    return {
      uptime_ms: Date.now() - HealthCheckService.startTime,
      memory_usage: process.memoryUsage(),
      node_version: process.version,
      environment: process.env.NODE_ENV || "development",
    };
  }

  static determineOverallStatus(
    services: HealthCheckResult[],
    dependencies: Record<string, HealthCheckResult>,
  ): "healthy" | "degraded" | "unhealthy" {
    const allChecks = [
      ...services,
      dependencies.database,
      dependencies.supabase,
      dependencies.redis,
    ];

    const unhealthyCount = allChecks.filter(
      (check) => check && check.status === "unhealthy",
    ).length;
    const degradedCount = allChecks.filter(
      (check) => check && check.status === "degraded",
    ).length;

    if (unhealthyCount > 0) {
      return "unhealthy";
    }
    if (degradedCount > 0) {
      return "degraded";
    }
    return "healthy";
  }
}

// Main health check endpoint - overall system health
health.get("/", async (c) => {
  try {
    const healthResult = await handleHealthCheck();
    c.status(healthResult.status as any);
    return c.json(healthResult.body);
  } catch (error) {
    console.error("Health check failed:", error);
    return c.json(
      createErrorResponse(
        "Health check failed",
        error instanceof Error ? error.message : "Unknown error",
      ),
      503,
    );
  }
});

// Quick health check for load balancers
health.get("/quick", async (c) => {
  try {
    const quickResult = await handleQuickHealthCheck();
    c.status(quickResult.status as any);
    return c.json(quickResult.body);
  } catch (error) {
    console.error("Quick health check failed:", error);
    return c.json(
      createErrorResponse("Quick health check failed"),
      503,
    );
  }
});

// Database-specific health check
health.get("/database", async (c) => {
  try {
    const connectionTest = await testDatabaseConnection();

    if (!connectionTest.connected) {
      return c.json(
        createErrorResponse("Database connection failed", connectionTest.error),
        503,
      );
    }

    return c.json(
      createSuccessResponse({
        status: "healthy",
        connected: connectionTest.connected,
        latency: connectionTest.latency,
        timestamp: new Date().toISOString(),
      }),
    );
  } catch (error) {
    console.error("Database health check failed:", error);
    return c.json(
      createErrorResponse("Database health check failed"),
      503,
    );
  }
});

// Security and RLS validation check
health.get("/security", async (c) => {
  try {
    const rlsResult = await handleRLSValidation();
    c.status(rlsResult.status as any);
    return c.json(rlsResult.body);
  } catch (error) {
    console.error("Security health check failed:", error);
    return c.json(
      createErrorResponse("Security health check failed"),
      503,
    );
  }
});

// Environment configuration validation
health.get("/environment", async (c) => {
  try {
    const envValidation = validateDatabaseEnvironment();

    if (!envValidation.valid) {
      return c.json(
        createErrorResponse("Environment validation failed", envValidation.issues),
        500,
      );
    }

    return c.json(
      createSuccessResponse({
        status: "valid",
        issues: envValidation.issues,
        timestamp: new Date().toISOString(),
      }),
    );
  } catch (error) {
    console.error("Environment validation failed:", error);
    return c.json(
      createErrorResponse("Environment validation failed"),
      500,
    );
  }
});

// Supabase-specific health check
health.get("/health/supabase", async (c) => {
  const supabaseHealth = await HealthCheckService.checkSupabaseHealth();
  const statusCode = supabaseHealth.status === "healthy"
    ? 200
    : supabaseHealth.status === "degraded"
    ? 200
    : 503;

  return c.json(
    {
      healthy: supabaseHealth.status === "healthy",
      ...supabaseHealth,
    },
    statusCode,
  );
});

// Detailed health check for all AI services
health.get("/health/ai-services", async (c) => {
  try {
    // Check each AI service health
    const aiServiceChecks = await Promise.allSettled([
      fetch(`${process.env.API_BASE_URL}/api/ai/universal-chat/health`).then(
        (r) => r.json(),
      ),
      fetch(`${process.env.API_BASE_URL}/api/ai/feature-flags/health`).then(
        (r) => r.json(),
      ),
      fetch(`${process.env.API_BASE_URL}/api/ai/cache/health`).then((r) => r.json()),
      fetch(`${process.env.API_BASE_URL}/api/ai/monitoring/health`).then((r) => r.json()),
      fetch(
        `${process.env.API_BASE_URL}/api/ai/no-show-prediction/health`,
      ).then((r) => r.json()),
      fetch(
        `${process.env.API_BASE_URL}/api/ai/appointment-optimization/health`,
      ).then((r) => r.json()),
      fetch(`${process.env.API_BASE_URL}/api/ai/compliance/health`).then((r) => r.json()),
    ]);

    const serviceNames = [
      "universal-chat",
      "feature-flags",
      "cache-management",
      "monitoring",
      "no-show-prediction",
      "appointment-optimization",
      "compliance-automation",
    ];

    const serviceResults = aiServiceChecks.map((result, index) => ({
      service: serviceNames[index],
      status: result.status === "fulfilled" && (result.value as any)?.healthy
        ? "healthy"
        : "unhealthy",
      details: result.status === "fulfilled"
        ? result.value
        : { error: result.reason?.message },
    }));

    const unhealthyServices = serviceResults.filter(
      (s) => s.status === "unhealthy",
    ).length;
    const overallStatus = unhealthyServices === 0
      ? "healthy"
      : unhealthyServices < serviceResults.length / 2
      ? "degraded"
      : "unhealthy";

    return c.json(
      {
        healthy: overallStatus === "healthy",
        overall_status: overallStatus,
        services: serviceResults,
        summary: {
          total_services: serviceResults.length,
          healthy_services: serviceResults.filter((s) => s.status === "healthy")
            .length,
          unhealthy_services: unhealthyServices,
        },
      },
      overallStatus === "healthy" ? 200 : 503,
    );
  } catch (error) {
    return c.json(
      {
        healthy: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      },
      503,
    );
  }
});

export default health;
