{"name": "neonpro", "private": true, "scripts": {"preinstall": "echo 'Using Bun package manager for optimal performance'", "build": "turbo run build", "dev": "turbo run dev", "dev:api": "turbo dev --filter=api", "dev:web": "turbo dev --filter=web", "dev:full": "turbo dev", "start": "bun run dev", "clean": "turbo run clean", "test:unit": "vitest run --coverage --pool=threads --poolOptions.threads.singleThread=true", "test:unit:bun": "vitest run --no-coverage --pool=threads --poolOptions.threads.singleThread=true --reporter=verbose --isolate=false", "test:bun:fast": "vitest run --no-coverage --pool=threads --poolOptions.threads.singleThread=true --reporter=verbose --isolate=false --css=false", "test:bun:integration": "vitest run --config vitest.config.integration.ts --no-coverage --pool=threads --poolOptions.threads.singleThread=true", "test:watch": "vitest --watch --pool=threads --poolOptions.threads.singleThread=true", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:lint": "bun run lint && bun run type-check", "test:type": "turbo run type-check", "test:all": "bun run test:lint && bun run test:type && bun run test:unit:bun", "quality:check": "bun run test:lint && bun run test:type", "format": "dprint fmt", "format:check": "dprint check", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "trunk": "trunk check --all", "ci:check": "bun run quality:check && bun run test", "ci:fix": "bun run lint:fix && bun run format", "test": "turbo run test", "test:unit:watch": "vitest --reporter=verbose", "test:unit:ui": "vitest --ui", "test:unit:coverage": "turbo run test:coverage", "test:integration": "vitest run --config vitest.config.integration.ts", "test:integration:watch": "vitest --config vitest.config.integration.ts", "test:integration:ui": "vitest --ui --config vitest.config.integration.ts", "test:integration:coverage": "vitest run --coverage --config vitest.config.integration.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:install": "playwright install --with-deps", "test:safe": "turbo run test --filter=!@neonpro/ai --filter=!@neonpro/compliance", "test:unit:safe": "turbo run test:unit --filter=!@neonpro/ai --filter=!@neonpro/compliance", "test:healthcare": "turbo run test --filter=@neonpro/healthcare", "build:production": "NODE_ENV=production turbo run build", "validate:production": "turbo run lint && turbo run type-check && turbo run test:safe", "deploy:vercel": "vercel --prod", "deploy:staging": "vercel", "health:check": "turbo run health:check", "monitor:production": "turbo run monitor", "test:ai": "vitest run --testNamePattern=\"AI|LLM|GPT\" --reporter=verbose", "benchmark": "vitest bench --reporter=verbose", "security:audit": "bun audit", "security:check": "bun run security:audit && bun run ci:check", "deps:update": "bun update", "deps:check": "bun outdated", "oxlint:fix": "npx oxlint apps packages --fix", "prepare": "husky", "install": "bun install", "postinstall": "bun run build"}, "devDependencies": {"@oxlint/win32-x64": "^1.13.0", "@playwright/test": "^1.54.2", "@rollup/rollup-win32-x64-msvc": "^4.49.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@turbo/gen": "^1.13.4", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "dprint": "^0.50.1", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "msw": "^2.10.5", "node-mocks-http": "^1.17.2", "oxc": "^1.0.1", "oxlint": "^1.13.0", "playwright": "^1.55.0", "prettier": "^3.6.2", "sonner": "^2.0.7", "turbo": "^2.5.6", "turbo-windows-64": "^2.5.6", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "packageManager": "bun@1.2.21", "engines": {"node": ">=18"}, "workspaces": ["apps/*", "apps/docs", "packages/*", "packages/typescript-config", "tools/*"], "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.0.4", "@sentry/nextjs": "^10.5.0", "@tanstack/react-query": "^5.62.2", "@tanstack/react-query-devtools": "^5.62.2", "@vitejs/plugin-react": "^5.0.0", "jspdf": "^3.0.1", "lru-cache": "^11.0.2", "lucide-react": "^0.541.0", "next": "^15.1.0", "optional": "^0.1.4", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "tailwindcss": "^3.4.15", "xlsx": "0.18.5"}, "pnpm": {"overrides": {"react": "^19.1.1", "react-dom": "^19.1.1", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "glob": "^10.3.10", "rimraf": "^5.0.5", "uuid": "^10.0.0", "jose": "^5.6.3", "inflight": "^1.0.6", "source-map": "^0.8.0-beta.0", "xlsx": ">=0.19.3", "marked": ">=4.0.10", "hawk": ">=9.0.1"}, "peerDependencyRules": {"ignoreMissing": ["@algolia/client-search"], "allowAny": ["*"]}}}